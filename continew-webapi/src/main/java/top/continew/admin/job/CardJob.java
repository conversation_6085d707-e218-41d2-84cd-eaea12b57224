/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.job;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardStatusEnum;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.gmail.GzyGMailHelper;
import top.continew.admin.biz.gmail.ItlGMailHelper;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.katai.strategy.impl.ItlOpsStrategyImpl;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.resp.CardResp;
import top.continew.admin.biz.robot.MyTelegramBot;
import top.continew.admin.biz.service.CardBalanceService;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.biz.service.CardTransactionService;
import top.continew.starter.core.exception.BusinessException;

import java.math.BigDecimal;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.time.LocalDateTime;
import java.util.List;

@Component
@RequiredArgsConstructor
public class CardJob {
    private final GzyGMailHelper gzyGMailHelper;
    private final ItlGMailHelper itlGMailHelper;
    private final CardService cardService;

    private final CardBalanceService cardBalanceService;

    private final CardTransactionService cardTransactionService;

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    private final MyTelegramBot telegramBot;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @JobExecutor(name = "syncCardData")
    public void syncCardData() {
        cardService.syncData(null);
        BigDecimal cvBalance = cardService.getPlatformBalance(CardPlatformEnum.CARD_VP);
        if (cvBalance.compareTo(BigDecimal.valueOf(10000)) <= 0) {
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(-1002464669740L)
                .text("CARD_VP 充值额度剩余 %s ，请及时联系平台提额".formatted(NumberUtil.toStr(cvBalance)))
                .build()));
        }
    }

    @JobExecutor(name = "syncItlCardRemark")
    public void syncItlCardRemark(JobArgs jobArgs) {
        if (jobArgs.getJobParams() != null) {
            String params = (String)jobArgs.getJobParams();
            if (StringUtils.isNotBlank(params)) {
                redisTemplate.opsForValue().set(ItlOpsStrategyImpl.ITL_API_AUTH, params);
            }
        }

        List<CardDO> cards = cardService.list(new LambdaQueryWrapper<CardDO>().eq(CardDO::getPlatform, CardPlatformEnum.INTERLACE)
                .isNotNull(CardDO::getPlatformAdId).ne(CardDO::getPlatformAdId, StrUtil.EMPTY));

        for (CardDO card : cards) {
            card.setRemark(card.getPlatformAdId());
            cardOpsStrategyFactory.findStrategy(CardPlatformEnum.INTERLACE).updateRemark(card);
        }
    }

    @JobExecutor(name = "syncCardFirstPageData")
    public void syncCardFirstPageData() {
        cardService.syncData(1);
    }

    @JobExecutor(name = "syncCardBillData")
    public void syncCardBillData(JobArgs jobArgs) {
        int minutes = 30;
        if (jobArgs.getJobParams() != null) {
            String params = (String)jobArgs.getJobParams();
            if (StringUtils.isNotBlank(params)) {
                minutes = Integer.parseInt((String)jobArgs.getJobParams());
            }
        }
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusMinutes(minutes);
        cardBalanceService.syncData(start, end);
        CardOpsStrategy photonOpsStrategy = cardOpsStrategyFactory.findStrategy(CardPlatformEnum.PHOTON_PAY);
        BigDecimal photonBalance = photonOpsStrategy.getCurrentBalance();
        if (photonBalance.compareTo(BigDecimal.valueOf(10000)) <= 0) {
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(-1002464669740L)
                .text("光子易 余额剩余 %s ，请及时打款充值".formatted(NumberUtil.toStr(photonBalance)))
                .build()));
        }
        CardOpsStrategy itlStrategy = cardOpsStrategyFactory.findStrategy(CardPlatformEnum.INTERLACE);
        BigDecimal itlBalance = itlStrategy.getCurrentBalance();
        if (itlBalance.compareTo(BigDecimal.valueOf(10000)) <= 0) {
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(-1002464669740L)
                .text("interlace 余额剩余 %s ，请及时打款充值".formatted(NumberUtil.toStr(photonBalance)))
                .build()));
        }
    }

    @JobExecutor(name = "syncCardTransactionData")
    public void syncCardTransactionData(JobArgs jobArgs) {
        int minutes = 30;
        if (jobArgs.getJobParams() != null) {
            String params = (String)jobArgs.getJobParams();
            if (StringUtils.isNotBlank(params)) {
                minutes = Integer.parseInt((String)jobArgs.getJobParams());
            }
        }
        LocalDateTime end = LocalDateTime.now().withSecond(0).withNano(0);
        LocalDateTime start = end.minusMinutes(minutes);
        cardTransactionService.syncData(start, end);
    }

    @JobExecutor(name = "loadGzyCardSensitiveInfo")
    public void loadGzyCardSensitiveInfo() {
        //更新卡片的卡号
        cardService.loadCardSensitiveInfo(CardPlatformEnum.PHOTON_PAY);
        cardService.loadCardSensitiveInfo(CardPlatformEnum.INTERLACE);

        //更新光子易的卡片余额流水的卡号
        cardBalanceService.updateCardNumber(CardPlatformEnum.PHOTON_PAY);

        //更新卡片交易流水的卡号
        cardTransactionService.updateCardNumber(CardPlatformEnum.PHOTON_PAY);
        cardTransactionService.updateCardNumber(CardPlatformEnum.AMZ);
        cardTransactionService.updateCardNumber(CardPlatformEnum.INTERLACE);
    }

    @JobExecutor(name = "loadGzyCardHolderId")
    public void loadGzyCardHolderId() {
        //更新光子易卡片的用卡人ID
        cardService.loadGzyCardHolderId();
    }

    @JobExecutor(name = "updateGzyCardBalance")
    public void updateCardUsedAmount() {
        cardService.updateCardUsedAmount(CardPlatformEnum.PHOTON_PAY);
        cardService.updateCardUsedAmount(CardPlatformEnum.AMZ);
        cardService.updateCardUsedAmount(CardPlatformEnum.INTERLACE);
    }

    @JobExecutor(name = "checkGzyEmails")
    public void checkGzyEmails() {
        //获取新的授权验证码
        try {
            gzyGMailHelper.checkNewEmails();

        } catch (SocketTimeoutException | SocketException e) {
            throw new BusinessException("获取gzy邮件超时");
        }


        try {
            itlGMailHelper.checkNewEmails();

        } catch (SocketTimeoutException | SocketException e) {
            throw new BusinessException("获取itl邮件超时");
        }
    }

    @JobExecutor(name = "syncCardClearTransactionDataJob")
    public void syncCardClearTransactionData() {
        cardTransactionService.syncClearTransactionData();
    }

    @JobExecutor(name = "convertCardTransactionTimeJob")
    public void convertCardTransactionTime() {
        // 计算一天前的开始时间
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0);
        

        cardTransactionService.updateAdAccountId(startTime, endTime);
        cardTransactionService.updateCustomerId(startTime, endTime);
        
    }

    @JobExecutor(name = "withdrawCardBalanceJob")
    public void withdrawCardBalance() {
        //FIXME 考虑AMZ
        List<CardResp> cardList = cardService.getNeedWithdrawCard();
        for (CardResp cardResp : cardList) {
            CardDO cardDO = new CardDO();
            cardDO.setCardNumber(cardResp.getCardNumber());
            cardDO.setPlatformCardId(cardResp.getPlatformCardId());
            CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(cardResp.getPlatform());
            try {
                cardOpsStrategy.withdrawCard(cardDO, null);
                if (cardResp.getStatus().equals(CardStatusEnum.NORMAL) && (cardResp.getAccountStatus()
                    .equals(AdAccountStatusEnum.BANNED) || cardResp.getSaleStatus()
                    .equals(AdAccountSaleStatusEnum.INVALID))) {
                    cardOpsStrategy.inactiveCard(cardDO);
                }
            } catch (Exception ignored) {

            }
        }
    }
}

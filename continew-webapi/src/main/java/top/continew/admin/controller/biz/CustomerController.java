/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.excel.CustomerDailyExcel;
import top.continew.admin.biz.model.entity.CustomerActivationReq;
import top.continew.admin.biz.model.entity.CustomerBusinessUserDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.CustomerOrderStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerQuery;
import top.continew.admin.biz.model.query.ToufangCustomerQuery;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.CustomerBusinessUserService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseController;
import cn.crane4j.core.support.OperateTemplate;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 客户管理 API
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Slf4j
@Tag(name = "客户管理 API")
@RestController
@RequiredArgsConstructor
@CrudRequestMapping(value = "/biz/customer", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT,
        Api.LIST})
public class CustomerController extends BaseController<CustomerService, CustomerResp, CustomerDetailResp, CustomerQuery, CustomerReq> {

    private final CustomerBusinessUserService customerBusinessUserService;

    @Operation(summary = "修改余额", description = "修改余额")
    @PutMapping("/{id}/balance")
    public void changeBalance(@PathVariable Long id, @Validated @RequestBody CustomerBalanceChangeReq req) {
        this.baseService.changeAmount(id, req);
    }

    @Log(ignore = true)
    @Operation(summary = "检查打款金额", description = "检查打款金额")
    @PostMapping("/{id}/transfer/check")
    public boolean checkRepeatTransfer(@PathVariable Long id,
                                       @Validated @RequestBody CheckCustomerRepeatTransferReq req) {
        return this.baseService.checkRepeatTransfer(id, req.getAmount());
    }

    @Operation(summary = "导出客户日报", description = "导出客户日报")
    @GetMapping("/daily/export")
    public void exportCustomerStatReport(Long id, HttpServletResponse response) {
        CustomerDO customerDO = baseService.getById(id);
        List<CustomerDailyExcel> customerDailyExcels = baseService.dailyList(customerDO);
        String exportFileName = URLUtil.encode("%s_%s.xlsx".formatted(customerDO.getName() + "-日报", DateUtil
                .format(new Date(), "yyyyMMddHHmmss")));
        response.setHeader("Content-disposition", "attachment;filename=" + exportFileName);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");

        try (InputStream stream = ResourceUtil.getStream("templates/dailyTemplate.xlsx");
             ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(stream).inMemory(true).build()) {

            WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
            excelWriter.fill(customerDailyExcels, writeSheet);
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            workbook.getCreationHelper().createFormulaEvaluator().evaluateAll();
        } catch (IOException e) {
            log.error("导出 Excel 出现错误: {}. fileName: {}.", e.getMessage(), exportFileName, e);
            throw new BusinessException("导出 Excel 出现错误");
        }
    }

    @Log(ignore = true)
    @GetMapping("/info")
    public List<CustomerInfoResp> info(Long id) {
        CustomerInfoResp info = baseService.getInfo(id);
        return info == null ? List.of() : List.of(info);
    }

    @Log(ignore = true)
    @GetMapping("/orderStatisticsPage")
    public BasePageResp<CustomerOrderStatisticsResp> page(CustomerOrderStatisticsQuery query, PageQuery pageQuery) {
        return baseService.selectCustomerOrderStatisticsPage(query, pageQuery);
    }


    @Operation(summary = "批量修改客户状态", description = "批量修改客户状态")
    @PutMapping("/batch-update-status")
    public void batchUpdateStatus(@RequestBody @Validated BatchUpdateStatusReq req) {
        baseService.batchUpdateStatus(req.getIds(), req.getStatus(), req.getTerminateTime());
    }

    @Operation(summary = "开通账户", description = "开通账户")
    @PutMapping("/{id}/open")
    @SaCheckPermission("biz:customer:open")
    public CustomerOpenResp open(@PathVariable Long id, @Validated @RequestBody CustomerOpenReq req) {
        return baseService.open(id, req);
    }

    @Operation(summary = "重置密码", description = "重置密码")
    @PutMapping("/{id}/resetPassword")
    @SaCheckPermission("biz:customer:open")
    public CustomerOpenResp resetPassword(@PathVariable Long id, @RequestBody CustomerOpenReq req) {
        return baseService.resetPassword(id,req);
    }

    @PostMapping("/activation")
    @SaCheckPermission("biz:customer:activation")
    public void activation(@RequestBody @Validated CustomerActivationReq req) {
        baseService.activation(req);
    }

    @Operation(summary = "批量修改关联商务", description = "批量修改关联商务")
    @PutMapping("/batchUpdateBusinessUser")
    @SaCheckPermission("biz:customer:updateBusinessUser")
    public void batchUpdateBusinessUser(@RequestBody @Validated CustomerBatchUpdateBusinessUserReq req) {
        baseService.batchUpdateBusinessUser(req.getIds(), req.getBusinessUserId());
    }

    @Operation(summary = "查询客户商务分配", description = "根据客户ID查询该客户的所有商务用户分配信息")
    @GetMapping("/{id}/businessUsers")
    public List<CustomerBusinessUserResp> getCustomerBusinessUsers(@PathVariable Long id) {
        return customerBusinessUserService.getByCustomerId(id);
    }

    @Log(ignore = true)
    @GetMapping("toufang/page")
    public PageResp<ToufangCustomerResp> getToufangCustomerPage(ToufangCustomerQuery customerQuery, PageQuery pageQuery) {
        return this.baseService.selectToufangCustomerPage(customerQuery, pageQuery);
    }

    @Log(ignore = true)
    @GetMapping("toufang/export")
    public void getToufangCustomerPage(ToufangCustomerQuery customerQuery, SortQuery sortQuery, HttpServletResponse response) {
        List<ToufangCustomerResp> list = this.baseService.selectToufangCustomerList(customerQuery, sortQuery);
        ExcelUtils.export(list, "导出数据", ToufangCustomerResp.class, response);
    }

    @Log(ignore = true)
    @GetMapping("toufang/report/export")
    public void exportToufangCustomerDailyReport(Long customerId, HttpServletResponse response) {
        this.baseService.exportToufangCustomerDailyReport(customerId, response);
    }

    @Log(ignore = true)
    @PostMapping("toufang/refund")
    public void refund(@Validated @RequestBody ToufangRefundReq req) {
        this.baseService.toufangRefund(req);
    }

    @Log(ignore = true)
    @GetMapping("toufang/daily/page")
    public PageResp<Map<String, Object>> getToufangCustomerDailyPage(Long customerId, PageQuery pageQuery) {
        return baseService.getToufangCustomerDailyPage(customerId,pageQuery);
    }

    @Log(ignore = true)
    @GetMapping("toufang/daily/export")
    public void exportToufangCustomerDaily(Long customerId, HttpServletResponse response) {
         baseService.exportToufangCustomerDaily(customerId, response);
    }

}
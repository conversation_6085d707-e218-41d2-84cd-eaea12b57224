package top.continew.admin.biz.model.resp;

import lombok.Data;

@Data
public class SalesDailyStatsResp {

    private String name;
    private Integer totalCount;
    private Integer wechatCount;
    private Integer telegramCount;
    private Integer 
    public SalesDailyStatsResp(String name, int totalCount, int wechatCount, int telegramCount) {
        this.name = name;
        this.totalCount = totalCount;
        this.wechatCount = wechatCount;
        this.telegramCount = telegramCount;
    }
}
